/*  Global Variables (Root Level) */
:root {
    --primary-color: #2563eb;
    --primary-light: #3b82f6;
    --primary-dark: #1d4ed8;
    --secondary-color: #dbeafe;
    --dark-color: #1f1f1f;
    --text-color: #1f1f1f;
    --background-color: #f0f1f6;
    --white-color: #ffffff;
    --border-radius: 15px;
    --black: #000;
    --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    --auth-bg: linear-gradient(135deg, #3b82f6 0%, #05162e 100%);
    --glass-bg: rgba(255, 255, 255, 0.95);
    --glass-border: rgba(255, 255, 255, 0.2);
    --input-border: #e5e7eb;
    --input-focus: #2563eb;
    --text-muted: #6b7280;
    --text-dark: #374151;
}

/*  Global Styles */
*{
    transition-duration: 0.2s;
}
body {
    font-family: "Inter", "Manrope", serif;
    font-optical-sizing: auto;
    font-weight: 400;
    font-style: normal;
    line-height: 1.6;
    background-color: var(--background-color);
    color: var(--text-color);
    padding: 25px;
    font-size: 14px;
}

/* Authentication Pages Body Override */
body.auth-page {
    font-family: 'Inter', sans-serif;
    background: var(--auth-bg);
    min-height: 100vh;
    padding: 0;
    margin: 0;
}
::-webkit-scrollbar {
    width: 12px;
    scroll-behavior: smooth;
}

::-webkit-scrollbar-track {
    background: var(--white-color);
    scroll-behavior: smooth;
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: 20px;
    border: 3px solid var(--white-color);
    scroll-behavior: smooth;
}

.scroll-auto{
    height: 100%;
    overflow: auto;
}
.logo {
    margin-bottom: 20px;
    display: block;
}
.logo img{
    height: 50px;
    margin: 13px;
}
a{
    text-decoration: none;
}
ul{
    list-style: none;
}


/* Utilities */
.mt-4 { margin-top: 20px; }
.mb-4 { margin-bottom: 20px; }
.f-28{
    font-size: 28px;
}
.f-22{
    font-size: 22px;
}
.separator{
    border-bottom: 1px solid #eee;
    height: 1px;
}


/* Left Menu Styles */
.left-menu{
    background: var(--white-color);
    padding: 15px;
    border-radius: var(--border-radius);
    width: 275px;
    height: calc(100% - 50px);
    position: fixed;
}
.menu-items{
    height: calc(100% - 80px);
    overflow: auto;
}
.toggle-menu .side-menu-item {
    padding: 12px 30px 12px 19px;
    margin: 0;
    color: #2e384d;
    border-radius: var(--border-radius);
    overflow: hidden;
}
ul.side-menu.toggle-menu {
    padding: 0;
    margin: 0;
}
.side-menu-item{
    display: block;
}
.side-menu-item {
    position: relative;
    display: flex;
    align-items: center;
    padding: 10px 19px 10px 19px;
    font-size: 14px;
    font-weight: 400;
    margin: 1px 0;
    transition: border-left-color 0.3s ease, background-color 0.3s ease;
    border-bottom: 1px dotted rgba(255,255,255,0.1);
    border-radius: 7px;
}
.side-menu-icon {
    flex: 0 0 auto;
    width: 25px;
    font-size: 18px;
}
.side-menu-label {
    white-space: nowrap;
    flex: 1 1 auto;
    font-weight: 700;
}
.slide-menu {
    max-height: 0;
    padding-left: 20px;
    overflow: hidden;
    transition: max-height 0.3s ease;
}
.slide.is-expanded .slide-menu {
    max-height: 100%;
}
.toggle-menu.side-menu li ul li a {
    border-top: 0;
    padding: 5px 10px 5px 0px;
    color: var(--dark-color);
    display: block;
    font-size: 14px;
    font-weight: 600;
}
.side-menu li ul li a::before {
    content: "\f101";
    font-family: FontAwesome;
    font-style: normal;
    font-weight: normal;
    text-decoration: inherit;
    padding-right: 0.5em;
    top: 10px;
    font-size: 12px;
    opacity: 0.4;
    margin-right: 8px;
}
.toggle-menu .slide.is-expanded .fa-angle-right {
    -webkit-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg);
}
.side-menu-item:hover, .is-expanded .side-menu-item{
    background: var(--secondary-color);
    border-radius: var(--border-radius);
    color: var(--black);
}
.side-menu li ul li a:hover{
    color: var(--primary-color);
}

/*Top Area Styles*/
.top-area {
    background: var(--white-color);
    padding: 15px;
    border-radius: var(--border-radius);
    width: calc(100vw - 340px);
    margin-left: auto;
    position: fixed;
    right: 20px;
    z-index: 1000;
}

/*footer Area Styles*/
.footer {
    background: var(--white-color);
    padding: 15px;
    border-radius: var(--border-radius);
    width: calc(100vw - 340px);
    margin-left: auto;
    position: fixed;
    right: 20px;
    bottom: 25px;
}
.footer p{
    margin: 0;
}


/*form elements*/
input.form-control {
    background: var(--secondary-color);
    border-radius: var(--border-radius);
    padding: 15px;
    border-color: transparent !important;
}
.btn-primary{
    background: var(--gradient-primary);
    border: none;
    transition: all 0.15s ease;
    position: relative;
}
.btn-primary:hover{
    background: var(--gradient-primary);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}
.btn-default{
    background: var(--secondary-color);
    transition: all 0.15s ease;
    position: relative;
}
.btn-default:hover{
    background: #d2d6e2 !important;
}
.btn{
    height: 56px;
    border-radius: var(--border-radius);
}

/* Remove default button focus outline */
.btn:focus,
.btn:active,
.btn:focus-visible {
    outline: none !important;
    box-shadow: none !important;
}

a.btn{
    line-height: 42px;
}

/*dropdown elements*/
.custom-dropdown {
    border: 0px;
    width: 280px;
    box-shadow: 0px 0px 5px #dbdbdb;
    z-index: 10000 !important;
}
/* Active states - override hover states completely to prevent fluctuation */
.btn-primary:active {
    background: var(--gradient-primary) !important;
    border: none !important;
    transform: translateY(0px) !important;
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.4) !important;
    filter: brightness(0.92) !important;
    transition: none !important;
}

.btn-default:active {
    background: var(--secondary-color) !important;
    border-color: var(--secondary-color) !important;
    transform: translateY(0px) !important;
    filter: brightness(0.88) !important;
    transition: none !important;
}

.btn-secondary:active {
    background: #e5e7eb !important;
    border-color: #d1d5db !important;
    transform: translateY(0px) !important;
    filter: brightness(0.92) !important;
    transition: none !important;
}

.btn-add-new:active {
    background: var(--gradient-primary) !important;
    transform: translateY(0px) !important;
    box-shadow: 0 3px 12px rgba(37, 99, 235, 0.4) !important;
    filter: brightness(0.92) !important;
    transition: none !important;
}

.empty-actions .btn-outline-primary:active {
    background: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
    transform: translateY(0px) !important;
    filter: brightness(0.92) !important;
    transition: none !important;
}

.empty-actions .btn-outline-secondary:active {
    background: #64748b !important;
    border-color: #64748b !important;
    color: white !important;
    transform: translateY(0px) !important;
    filter: brightness(0.92) !important;
    transition: none !important;
}

/* Generic fallback for other buttons */
.btn-check:checked+.btn, .btn.show, .btn:first-child:active, :not(.btn-check)+.btn:active {
    filter: brightness(0.92) !important;
    transform: translateY(0px) !important;
    transition: none !important;
}

/* Main Content*/
.main-content {
    width: calc(100vw - 303px);
    max-height: calc(100% - 228px);
    margin-left: auto;
    overflow: auto;
    margin-top: 105px;
    margin-right: -5px;
    position: fixed;
    right: 0;
}

.main-content.error-section {
    background: var(--white-color);
    padding: 15px;
    border-radius: var(--border-radius);
    width: 100% !important;
    height: calc(100vh - 50px) !important;
    position: relative;
    overflow: auto;
    margin-top: auto !important;
    margin-right: auto !important;
}

/* Error pages*/
.error-section {
    background: url(../images/pattern.png) #fff !important;
    background-size: cover !important;
    background-repeat: no-repeat !important;
    background-position: center center !important;
}
.error-section .error-area{
    align-items: center;
    justify-content: center;
    display: flex;
    flex-direction: column;
}
.error-section h1{
    font-size: 168px;
}

/*misc styles*/
.icon-button{
    width: 56px;
}
.profile-menu button{
    padding: 0;
    display: flex;
}
.profile-menu button img{
    border-radius: var(--border-radius);
    margin-right: 10px;
}
.profile-menu button p{
    margin: 0;
    text-align: left;
    font-size: 16px;
    font-weight: 600;
}
.profile-menu button span{
    font-size: 16px;
    color: #adadad;
}
.profile-menu button div {
    justify-content: center;
    align-items: flex-start;
    display: flex;
    flex-direction: column;
}
.profile-menu{
    border-left: 1px solid var(--secondary-color);
    margin-left: 11px !important;
}

/* Fix button focus and outline issues */
.profile-menu button:focus,
.profile-menu button:active,
.profile-menu button:focus-visible {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

/* Ensure dropdown appears above all content */
.dropdown-menu.show {
    z-index: 9999 !important;
}
.messages .custom-dropdown li a *{
    margin: 0px;
}
.messages .custom-dropdown li a p{
    font-weight: 600;
}
.messages .custom-dropdown li a span{
    color: #adadad;
}
.messages .custom-dropdown li a > div{
    display: flex;
}
.messages .custom-dropdown li a > div i {
    color: var(--primary-color);
    height: 50px;
    width: 50px;
    background: var(--secondary-color);
    line-height: 50px;
    text-align: center;
    border-radius: 15px !important;
    margin-right: 10px;
}
.messages .custom-dropdown{
    width: 350px;
}
.messages .custom-dropdown li a {
    padding: 10px;
}
.messages .bg-warning{
    background: #fff0c1 !important;
    color: #ffc107 !important;
}
.messages .bg-danger{
    background: #ffe4e6 !important;
    color: #dc3545 !important;
}
.m-show{
    display: none;
}
.m-hide{
    display: block;
}
.show-box{
    display: block !important;
}
.mw-100{
    max-width: 100%;
}
.mw-590{
    max-width: 590px  !important;
}

/* ========================================
   AUTHENTICATION PAGES STYLES
   ======================================== */

/* Authentication Container */
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

/* Authentication Card */
.auth-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--glass-border);
    padding: 48px;
    width: 100%;
    max-width: 440px;
}

.auth-card.wide {
    max-width: 520px;
}

/* Authentication Logo */
.auth-logo {
    width: auto;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 32px;
}

.auth-logo img {
    max-height: 100%;
    width: auto;
}

.auth-logo i {
    color: white;
    font-size: 24px;
}

/* Authentication Headers */
.auth-title {
    font-size: 28px;
    font-weight: 700;
    color: #1a1a1a;
    text-align: center;
    margin-bottom: 8px;
}

.auth-subtitle {
    color: var(--text-muted);
    text-align: center;
    margin-bottom: 40px;
    font-size: 16px;
    line-height: 1.5;
}

/* Form Styles */
.form-group {
    margin-bottom: 24px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.form-label {
    font-weight: 500;
    color: var(--text-dark);
    margin-bottom: 8px;
    font-size: 14px;
}

.auth-page .form-control {
    border: 2px solid var(--input-border);
    border-radius: 12px !important;
    padding: 16px;
    font-size: 16px;
    transition: all 0.2s ease;
    background: #ffffff;
}

.auth-page .form-control:focus {
    border-color: var(--input-focus);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
}

.input-group {
    position: relative;
}

.input-icon {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    z-index: 10;
}

.form-control.has-icon {
    padding-left: 48px;
}

.password-toggle {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    z-index: 10;
}

/* Button Styles */
.auth-page .btn-primary {
    background: var(--gradient-primary);
    border: none;
    border-radius: 12px;
    padding: 16px;
    font-weight: 600;
    font-size: 16px;
    width: 100%;
    margin-bottom: 24px;
    transition: all 0.2s ease;
}

.auth-page .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(37, 99, 235, 0.3);
}

/* Divider */
.divider {
    text-align: center;
    margin: 32px 0;
    position: relative;
    color: #9ca3af;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e5e7eb;
}

.divider span {
    background: var(--glass-bg);
    padding: 0 16px;
}

/* Social Buttons */
.social-btn {
    border: 2px solid var(--input-border);
    border-radius: 12px;
    padding: 12px;
    background: white;
    color: var(--text-dark);
    font-weight: 500;
    transition: all 0.2s ease;
    margin-bottom: 12px;
}

.social-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-1px);
}

/* Links */
.auth-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.auth-link:hover {
    color: var(--primary-dark);
}

/* Remember/Forgot Section */
.remember-forgot {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.text-center-bottom {
    text-align: center;
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e5e7eb;
    color: var(--text-muted);
}

/* Password Strength Indicator */
.password-strength {
    margin-top: 8px;
    font-size: 12px;
    color: var(--text-muted);
}

.strength-bar {
    height: 4px;
    background: #e5e7eb;
    border-radius: 2px;
    margin-top: 4px;
    overflow: hidden;
}

.strength-fill {
    height: 100%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.strength-weak { background: #ef4444; width: 25%; }
.strength-fair { background: #f59e0b; width: 50%; }
.strength-good { background: #10b981; width: 75%; }
.strength-strong { background: #059669; width: 100%; }

/* Success State Styles */
.success-state {
    text-align: center;
}

.success-icon {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
}

.success-icon i {
    color: white;
    font-size: 28px;
}

.success-title {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 12px;
}

.success-text {
    color: var(--text-muted);
    margin-bottom: 32px;
    line-height: 1.5;
}

/* Secondary Button */
.btn-secondary {
    background: #f3f4f6;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 16px;
    font-weight: 600;
    font-size: 16px;
    width: 100%;
    color: var(--text-dark);
    transition: all 0.2s ease;
}

.btn-secondary:hover {
    background: #e5e7eb;
    border-color: #d1d5db;
    color: var(--text-dark);
    transform: translateY(-1px);
}

/* Help Section */
.help-section {
    background: #f8fafc;
    border-radius: 12px;
    padding: 20px;
    margin-top: 24px;
    text-align: center;
}

.help-section h6 {
    color: var(--text-dark);
    font-weight: 600;
    margin-bottom: 8px;
}

.help-section p {
    color: var(--text-muted);
    margin-bottom: 12px;
    font-size: 14px;
}

/* Lockscreen Specific Styles */
.user-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin: 0 auto 24px;
    display: block;
    border: 4px solid rgba(37, 99, 235, 0.2);
}

.user-info {
    text-align: center;
    margin-bottom: 32px;
}

.user-name {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 4px;
}

.user-role {
    color: var(--text-muted);
    font-size: 14px;
    margin-bottom: 16px;
}

.lock-status {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
}

.time-display {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 16px;
    padding: 24px;
    text-align: center;
    margin-bottom: 32px;
    border: 1px solid #e2e8f0;
}

.current-time {
    font-size: 32px;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 4px;
}

.current-date {
    color: var(--text-muted);
    font-size: 14px;
}

.auth-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 24px;
}

.session-info {
    text-align: center;
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e5e7eb;
}

.session-info small {
    color: var(--text-muted);
    font-size: 12px;
}

.lock-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
}

.lock-icon i {
    color: white;
    font-size: 24px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }

    .auth-card {
        padding: 32px 24px;
    }

    .auth-actions {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .auth-actions .auth-link {
        justify-content: center;
    }
}

/* ========================================
   ENHANCED EMPTY PAGE STYLES
   ======================================== */

/* Enhanced Breadcrumb Styles */
.enhanced-breadcrumb {
    /* background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); */
    /* border: 1px solid #e2e8f0; */
    /* border-radius: 12px; */
    padding: 15px;
    margin-bottom: 0;
    /* box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); */
}

.enhanced-breadcrumb .breadcrumb-item {
    font-size: 14px;
    font-weight: 500;
}

.enhanced-breadcrumb .breadcrumb-item a {
    color: #64748b;
    text-decoration: none;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
}

.enhanced-breadcrumb .breadcrumb-item a:hover {
    color: var(--primary-color);
    transform: translateX(2px);
}

.enhanced-breadcrumb .breadcrumb-item.active {
    color: #1e293b;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.enhanced-breadcrumb .breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: #cbd5e1;
    font-size: 16px;
    font-weight: 600;
    margin: 0 12px;
}

/* Add New Button Styles */
.btn-add-new {
    background: var(--gradient-primary);
    border: none;
    border-radius: 10px;
    padding: 12px 24px;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.2);
}

.btn-add-new:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(37, 99, 235, 0.3);
    background: var(--gradient-primary);
}

/* Page Header Card */
.page-header-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    padding: 32px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: hidden;
}

.page-header-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.page-header-card .page-title {
    font-size: 32px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 8px;
    letter-spacing: -0.025em;
}

.page-header-card .page-subtitle {
    color: #64748b;
    font-size: 16px;
    margin-bottom: 0;
    font-weight: 400;
    line-height: 1.6;
}

/* Content Card */
.content-card {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    overflow: hidden;
}
.card .btn {
    padding: 0px 23px;
}

/* Enhanced Empty State */
.empty-state {
    text-align: center;
    padding: 80px 40px;
    background: linear-gradient(135deg, #fafbfc 0%, #f8fafc 100%);
}

.empty-icon {
    width: 120px;
    height: 120px;
    margin: 0 auto 32px;
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #bfdbfe;
    position: relative;
}

.empty-icon::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(37, 99, 235, 0.1) 100%);
    animation: pulse 2s infinite;
}

.empty-icon i {
    font-size: 48px;
    color: var(--primary-color);
    z-index: 1;
    position: relative;
}

.empty-title {
    font-size: 28px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 16px;
    letter-spacing: -0.025em;
}

.empty-description {
    font-size: 16px;
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 32px;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.empty-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

.empty-actions .btn {
    border-radius: 10px;
    padding: 12px 24px;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s ease;
}

.empty-actions .btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.empty-actions .btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(37, 99, 235, 0.3);
}

.empty-actions .btn-outline-secondary {
    border: 2px solid #64748b;
    color: #64748b;
}

.empty-actions .btn-outline-secondary:hover {
    background: #64748b;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(100, 116, 139, 0.3);
}

/* Pulse Animation */
@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .enhanced-breadcrumb {
        padding: 12px 16px;
    }

    .page-header-card {
        padding: 24px 20px;
    }

    .page-header-card .page-title {
        font-size: 24px;
    }

    .empty-state {
        padding: 60px 20px;
    }

    .empty-icon {
        width: 100px;
        height: 100px;
        margin-bottom: 24px;
    }

    .empty-icon i {
        font-size: 40px;
    }

    .empty-title {
        font-size: 24px;
    }

    .empty-description {
        font-size: 14px;
    }

    .empty-actions {
        flex-direction: column;
        align-items: center;
    }

    .empty-actions .btn {
        width: 100%;
        max-width: 200px;
    }

    .btn-add-new {
        padding: 10px 20px;
        font-size: 13px;
    }
}

@media (max-width: 576px) {
    .enhanced-breadcrumb .breadcrumb-item {
        font-size: 12px;
    }

    .enhanced-breadcrumb .breadcrumb-item + .breadcrumb-item::before {
        margin: 0 8px;
        font-size: 14px;
    }

    .page-header-card .page-title {
        font-size: 20px;
    }

    .page-header-card .page-subtitle {
        font-size: 14px;
    }
}
